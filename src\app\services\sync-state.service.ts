import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface SyncState {
  isReceiving: boolean;
  isSending: boolean;
  message: string;
  progress?: number;
}

@Injectable({
  providedIn: 'root'
})
export class SyncStateService {
  private syncStateSubject = new BehaviorSubject<SyncState>({
    isReceiving: false,
    isSending: false,
    message: ''
  });

  public syncState$ = this.syncStateSubject.asObservable();

  setReceivingState(isReceiving: boolean, message: string = '') {
    const currentState = this.syncStateSubject.value;
    this.syncStateSubject.next({
      ...currentState,
      isReceiving,
      message
    });
  }

  setSendingState(isSending: boolean, message: string = '') {
    const currentState = this.syncStateSubject.value;
    this.syncStateSubject.next({
      ...currentState,
      isSending,
      message
    });
  }

  getCurrentState(): SyncState {
    return this.syncStateSubject.value;
  }

  isAnySyncInProgress(): boolean {
    const state = this.getCurrentState();
    return state.isReceiving || state.isSending;
  }
}