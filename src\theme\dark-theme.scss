@import "primeng/resources/themes/md-dark-indigo/theme.css";

$background-color: rgb(36, 35, 35);
$primary-color: rgb(56, 56, 56);
$border-color: rgb(255, 255, 255);
$font-color: rgb(255, 255, 255);

body {
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vh;
  box-sizing: border-box;
}
ion-content {
  --ion-background-color: black !important;
  
}
ion-card{
  box-shadow: 0px 1px 7px white !important;
}
ion-card-content{
  height: 148px !important;
}
ion-card-content,ion-card-header{
  background-color: black !important;
  
}
ion-card-content{
  height: 200px !important;
}
ion-col{
  background-color: black !important;


}
ion-grid{
  color:#ffffff;
  background-color: black !important;

}
ion-grid ion-col {
  color: white;
}
form{
  background-color: black !important;

}
ion-inner-scroll {
  background-color: black !important;

}
ion-content[_ngcontent-ng-c2185411657] {
  --background: #000;
}

.container {
  background-color: $background-color;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;

  .card {
    width: 50vw;
    height: 50vh;
    background-color: $primary-color;
    border: 1px solid $border-color;

    .header {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .title {
      color: $font-color;
    }
  }
}
ion-card-title{
  color: white !important;
}
span{
  color: white !important;
}
ion-select,
ion-textarea,
ion-input {
  
  color: #ffffff !important; // Force text color to white
  border-bottom: 2px solid #ffffff ;
  
}

ion-select-option {
  color: #ffffff !important; // Set option text color to white
  
}
ion-alert .alert-button span {
  color: #491212 !important;
}

ion-note {
  color: #ffffff !important; // Set note color to white in dark mode
}
ion-label{
  color: white ; 
}
.status.new ion-label {
  color: #ffffff; /* White text color */
}

.status.waiting ion-label {
  color: #ffffff; /* White text color */
}

.status.accepted ion-label {
  color: #ffffff; /* White text color */
}

.status.review ion-label {
  color: #ffffff; /* White text color */
}

.status.refused ion-label {
  color: #ffffff; /* White text color */
}
.recommended-prospects-item strong {
  color: #ffffff; /* White text color */
}

.only-prospects-visited-item strong {
  color: #ffffff; /* White text color */
}
ion-item ion-label.label-floating {
  color: white !important;
}
ion-item ion-label {
  color: white !important;
}
ion-radio {
  --color: white;          /* Color of the filled circle when selected */
  --border-color: white;   /* Color of the outer circle */
}
.custom-background {
  background-color: white; 
  border-radius: 5px; 
  padding: 10px; /* Optional: Add padding for better spacing */
  margin-bottom: 5px; /* Optional: Space between items */
  color: #ffffff;
}
ion-alert input[type="date"]::-webkit-calendar-picker-indicator {

  filter: invert(1); /* Utilisez cette propriété pour inverser la couleur, utile en mode sombre */
}
.white-icon {
  color: white; /* Pour changer la couleur de l'icône */
}
ion-input {
  border-bottom: 2px solid #ffffff; /* Bordure sous les champs input */
}
.titre_h1{
  color: white;
}
.error-message {
  color: red;
  margin-bottom: 10px;
}







