import { Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { Product, Prospect } from '../models/marketing';
import { DbnameVersionService } from './dbname-version.service'; // Assurez-vous que le chemin d'importation est correct
import { TablesUpgrades } from 'src/app/upgrades/tables/tables';
import { environment } from 'src/environments/environment';
import { marketingAction } from '../models/marketing-action';

@Injectable({
  providedIn: 'root',
})
export class ActionMarketingService {
  private mDb!: SQLiteDBConnection;
  private readonly tableName: string = 'marketing_action';
  public databaseName: string; // Initialiser avec le nom réel de la base de données
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;

  constructor(
    private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
}

  

 


  async getAllMarketingActions(): Promise<any[]> {
    try {
      if (!this.mDb) {
        throw new Error('Database connection is not available');
      }
      const results = await this.mDb.query(`SELECT * FROM ${this.tableName}`);
      console.log('Retrieved marketing actions:', results.values);
      return results.values ? results.values : [];
    } catch (err) {
      console.error('Unable to retrieve marketing actions', err);
      return [];
    }
  }

  async addMarketingAction(
    name: string,
    budget: number,
    productId: string,
    prospectId: string,
    description: string,
    date: number
  ): Promise<void> {
    try {
      console.log('Adding marketing action:', { name, budget, productId, prospectId, description, date });
      await this.mDb.run(
        `INSERT INTO ${this.tableName} (name, budget, product_id, prospect_id, description, marketingAction_date, synchronized, status)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          name,
          budget,
          productId,
          prospectId,
          description,
          date,
          0, // synchronized
          'NEW' // status
        ]
      );
      console.log('Marketing action added successfully');

      // Sauvegarder les modifications dans le store si sur la plateforme web
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }

      // Vérifier les données dans la base de données
      const actions = await this.getAllMarketingActions();
      console.log('Updated marketing actions:', actions);
    } catch (err) {
      console.error('Unable to add marketing action', err);
    }
  }
  async getAllProducts(): Promise<any[]> {
    try {
      const results = await this.mDb.query('SELECT id, name FROM product');
      return results.values ? results.values.map(product => ({
        ...product,
        id: product.id.toString()
      })) : [];
    } catch (err) {
      console.error('Unable to retrieve products', err);
      return [];
    }
  }
  
  async getAllProspects(): Promise<Prospect[]> {
    try {
      const results = await this.mDb.query('SELECT id, firstname, lastname FROM prospect');
      console.log('Prospects retrieved:', results.values);
      return results.values ? results.values.map(prospect => ({
        ...prospect,
        id: prospect.id.toString()
      })) : [];
    } catch (err) {
      console.error('Unable to retrieve prospects', err);
      return [];
    }
  }
  
  

  async updateMarketingAction(
    id: number,
    name: string,
    budget: number,
    productId: string,
    prospectId: string,
    description: string,
    date: number
  ): Promise<void> {
    const marketingAction = {
      name,
      budget,
      product_id: productId,
      prospect_id: prospectId,
      description,
      marketingAction_date: date,
      synchronized: 0,
      status: 'UPDATED'
    };
    try {
      await this.mDb.run(
        `UPDATE ${this.tableName} SET name = ?, budget = ?, product_id = ?, prospect_id = ?, description = ?, marketingAction_date = ?, synchronized = ?, status = ?
         WHERE id = ?`,
        [
          marketingAction.name,
          marketingAction.budget,
          marketingAction.product_id,
          marketingAction.prospect_id,
          marketingAction.description,
          marketingAction.marketingAction_date,
          marketingAction.synchronized,
          marketingAction.status,
          id
        ]
      );
      console.log('Marketing action updated successfully');

      // Sauvegarder les modifications dans le store si sur la plateforme web
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (err) {
      console.error('Unable to update marketing action', err);
    }
  }

  async deleteMarketingAction(id: number): Promise<void> {
    try {
      await this.mDb.run(
        `DELETE FROM ${this.tableName} WHERE id = ?`,
        [id]
      );
      console.log('Marketing action deleted successfully');

      // Sauvegarder les modifications dans le store si sur la plateforme web
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (err) {
      console.error('Unable to delete marketing action', err);
    }
  }
  async getMarketingAction(jsonAction: marketingAction): Promise <marketingAction> {
    let action = await this.sqliteService.findOneBy(this.mDb, " marketing_action ", {id: jsonAction.id});
    
    if (!action) {

      if (jsonAction.marketingActionDate && jsonAction.name && jsonAction.budget  && jsonAction.productId && jsonAction.prospectId) {
        // create a new author
        let action = new marketingAction();
        action.id = jsonAction.id;
        action.marketingActionDate = jsonAction.marketingActionDate;
        action.name = jsonAction.name;
        action.budget = jsonAction.budget ;
        action.productId = jsonAction.productId;
        action.status = jsonAction.status;
        action.synchronized = jsonAction.synchronized;
        action.prospectId = jsonAction.prospectId;


        
        if (jsonAction.description) {
          action.description = jsonAction.description;
        }

        await this.sqliteService.save(this.mDb, "marketing_action", action);
        // cette etape pour verifier que l'element est bien sauvgarde dans la table 
        action = await this.sqliteService.findOneBy(this.mDb, "marketing_action", { id: jsonAction.id });
        if (action) {
          return action;
        } else {
          return Promise.reject(`failed to getMarketingAction for id ${jsonAction.id}`);
        }
      } else {
        // notification not in the database
        let action = new marketingAction();
        action.id = -1;
        return action;
      }
    } else {
      return action;
    } 
  }
 // filtre  
async getMarketingActionByDay(currentDate: Date) {
  const selectQuery = `
      SELECT * FROM marketing_action WHERE (marketingAction_date = ?) AND (status<>'DELETED')  ORDER BY marketingAction_date
      `;
  const res = await this.mDb.query(selectQuery,[currentDate.setHours(0,0,0,0)]);
  return res.values || [];
}
async getNextActionRules(): Promise<any[]> {
  try {
    const query = `SELECT * FROM next_action_rule`;
    const result = await this.mDb.query(query);
    
    const rules = result.values || [];
    
    for (let rule of rules) {
      rule.prospects = await this.getProspectsForRule(rule.totalRevenue, rule.period);
    }
    
    return rules;
  } catch (err) {
    console.error('Unable to retrieve next action rules', err);
    return [];
  }
}
async getProspectsForRule(totalRevenue: number, period: number): Promise<any[]> {
  try {
    const endDate = new Date().getTime();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - period);
    
    const query = `
      SELECT 
        p.id, p.firstname, p.lastname,
        SUM(vp.order_quantity * pr.price) as total_revenue
      FROM prospect p
      JOIN visit v ON p.id = v.prospect_id
      JOIN visit_product vp ON v.id = vp.visit_id
      JOIN product pr ON vp.product_id = pr.id
      WHERE v.visit_date BETWEEN ? AND ?
      GROUP BY p.id
      HAVING total_revenue >= ?
      ORDER BY total_revenue DESC
    `;
    
    const result = await this.mDb.query(query, [
      startDate.getTime(),
      endDate,
      totalRevenue
    ]);
    
    return result.values || [];
  } catch (err) {
    console.error('Unable to retrieve prospects for rule', err);
    return [];
  }
}
}
