import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>roller, ToastController } from '@ionic/angular';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class PopupService {

  constructor(
    private alertController: AlertController,
    private router: Router,
private toastController:ToastController  ) {}

  async showAlert(title: string, message: string, goTo: string): Promise<void> {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK']
    });

    await alert.present();
    const { role } = await alert.onDidDismiss();

    if (goTo === 'login') {
      // Vous devez définir votre méthode logout dans un autre service ou composant
      // this.authService.logout();
    } else if (goTo) {
      this.router.navigate([goTo]);
    }
  }

  async showToasterAlert(title: string, message: string, type: 'success' | 'warning' | 'danger' | 'info') {
    const colorMap = {
      success: 'success',
      warning: 'warning',
      danger: 'danger',
      info: 'info'
    };
  
    const toast = await this.toastController.create({
      header: title,
      message: message,
      color: colorMap[type], // Maps type to the corresponding color
      duration: 10000
    });
  
    await toast.present();
  }
}