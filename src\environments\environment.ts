// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  serverUrl: 'http://localhost:3000',  // Add this line with your development server URL
  databaseNames:[
    {name:"starter_posts"},
    {name:"starter_employees"},
    {name:"starter_notifications"},
    {name:"starter_expenses"},
    {name :"starter_message"},
    {name :"starter_planning"},
    {name :"starter_tables"},
    {name:"starter_activities"},
    {name:"starter_marketing"},
    {name:"starter_visits"},
    {name :"starter_plannings"},
    {name :"starter_filter"},
  ],
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.

