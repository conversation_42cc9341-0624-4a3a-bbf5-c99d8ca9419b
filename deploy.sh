#!/bin/bash

# Update code from git
echo "Pulling latest code..."
git pull

# Build Docker image
echo "Building Docker image..."
docker build -t ionic-app .

# Stop and remove existing container if it exists
echo "Stopping and removing existing container..."
docker stop bn-mobile 2>/dev/null && docker rm bn-mobile 2>/dev/null

# Run new container
echo "Starting new container..."
docker run -d -p 8090:80 --name bn-mobile ionic-app

echo "Deployment complete."
