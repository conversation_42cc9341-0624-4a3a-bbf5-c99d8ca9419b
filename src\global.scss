/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import '@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";
/* Variables for colors */
$primary-color: #007bff;
$secondary-color: #f0f0f0;
$stat-ca-bg-color: #00c3ff;
$stat-obj-bg-color: #ffa500;
$card-bg-color: #fff;
$card-header-bg-color: #007bff;
$card-header-color: #fff;
$smiley-size: 50px;

/* Dark theme variables */
$dark-primary-color: #1e1e1e;
$dark-secondary-color: #003b54; // Updated background color
$dark-stat-ca-bg-color: #005f7f;
$dark-stat-obj-bg-color: #ff4510;
$dark-card-bg-color: #ffffff;
$dark-card-header-bg-color: #444;
$dark-card-header-color: #fff;

/* Header */
ion-header {
  ion-toolbar {
    background-color: $primary-color;
    color: #fff;

    &.dark-theme {
      background-color: $dark-primary-color;
    }
  }
}

/* Content */
ion-content {
  --padding-top: 16px;
  --padding-end: 16px;
  --padding-bottom: 16px;
  --padding-start: 16px;

  

  &.dark-theme {
    --ion-background-color: #003b54;
  }

  /* Header Container */
  .header-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 16px;

    ion-item {
      border: none;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  /* Filters Container */
  .filters-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    ion-item {
      flex: 1;
      margin-right: 8px;
      border: none;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  /* Stats Container */
  .stats-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .stat-box {
      flex: 1;
      padding: 16px;
      border-radius: 8px;
      color: #fff;
      text-align: center;

      &:first-child {
        background-color: $stat-ca-bg-color;
        margin-right: 8px;
      }

      &:last-child {
        background-color: $stat-obj-bg-color;
        margin-left: 8px;
      }

      &.dark-theme {
        &:first-child {
          background-color: $dark-stat-ca-bg-color;
        }

        &:last-child {
          background-color: $dark-stat-obj-bg-color;
        }
      }
    }
  }

  /* Smiley Container */
  .smiley-container {
    text-align: center;
    margin-bottom: 16px;

    img {
      width: $smiley-size;
      height: $smiley-size;
    }
  }

  /* Grid Cards */
  ion-grid {
    ion-row {
      margin-bottom: 16px;

      ion-col {
        .custom-card {
          background-color: $card-bg-color;
          border-radius: 8px;

          &.dark-theme {
            background-color: $dark-card-bg-color;
          }

          ion-card-header {
            background-color: $card-header-bg-color;
            color: $card-header-color;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;

            &.dark-theme {
              background-color: $dark-card-header-bg-color;
              color: $dark-card-header-color;
            }

            ion-card-title {
              font-size: 1.2rem;
            }
          }

          ion-card-content {
            canvas {
              max-width: 100%;
              height: auto;
            }
          }
        }
      }
    }
  }
}
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); // Ensures 3 cards per row
    gap: 16px;
    padding: 16px;
  }
  
  ion-card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background-color: #ffff;
  }
  
  ion-card-title {
    font-size: 1.2em;
    font-weight: bold;
    text-align: center;
  }
  ion-card-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column; 
  }
  ion-card-content img {
    max-width: 100px;
    margin-bottom: 10px;
  }
  
  #container {
    padding: 16px;
    text-align: center;
  }
  
  #home-list ion-item {
    --background: #f9f9f9;
    margin: 8px 0;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  #home-list ion-item:hover {
    --background: #e0e0e0;
  }
   ion-tab-button img {
        width: 24px;
        height: 24px;
        margin-bottom: 4px;
      }
  
      ion-tab-button ion-label {
        font-size: 12px;
      }
      ion-content {
        --ion-background-color: #ffff;
        &.dark-theme {
          --ion-background-color: #003b54;
        }
      }
      ion-tabs{
        --ion-background-color: #ffff;
        &.dark-theme {
          --ion-background-color: #003b54;
        }
      }
      
      $border-color: #ddd;
      $table-header-bg: #1976d2;
      $table-header-color: #fff;
      $secondary-color: #f5f5f5;
      ion-grid {
          background-color: #fff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
          ion-row {
            border-bottom: 1px solid $border-color;
        
            &:first-child {
              background-color: $table-header-bg;
              color: $table-header-color;
              font-weight: bold;
            }
        
            &:nth-child(even) {
              background-color: $secondary-color;
            }
        
          
        
            ion-col {
              padding: 10px;
            }
          }
        }
        // Ajuster le body quand la console est visible
body.console-visible {
  padding-bottom: 200px;
}


// Forcer l'affichage de la console
app-console {
  display: block !important;
  position: relative !important;
  z-index: 99999 !important;
}

.console-container {
  display: flex !important;
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 200px !important;
  background: #ff0000 !important; // Rouge pour debug
  z-index: 99999 !important;
}

// Styles existants...


// Styles existants...

.sync-status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;

  // Remplacement du background et animation
  background: linear-gradient(90deg, #488AFF, #ffffff, #488AFF);
  background-size: 200% 100%;
  animation: loadingAnimation 1.5s infinite linear;

  color: white;
  padding: 18px 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  .sync-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    margin-top: 4px;

    ion-icon {
      font-size: 16px;
    }
  }
}

// Animation pour l'icône de synchronisation
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.spinning {
  animation: spin 2s linear infinite;
}

// Animation de loading header
@keyframes loadingAnimation {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

// Modifier le header pendant la synchronisation
app-header.syncing {
  margin-top: 4px; // Décaler pour faire place à la barre de sync
  transition: margin-top 0.3s ease;
}

// Styles pour le contenu pendant la sync
ion-content {
  --padding-top: 0;
  
  &.syncing {
    --padding-top: 60px;
  }
}
