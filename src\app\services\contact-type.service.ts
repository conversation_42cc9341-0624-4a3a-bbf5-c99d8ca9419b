import { Inject, Injectable } from "@angular/core"
import  { SQLiteService } from "./sqlite.service"
import  { ContactType } from "../models/contact-type"
import  { SQLiteDBConnection } from "@capacitor-community/sqlite"
import  { DbnameVersionService } from "./dbname-version.service"
import { environment } from "src/environments/environment"
import { Observable,  Observer } from "rxjs"
import { TablesUpgrades } from "../upgrades/tables/tables"
import  { DbCreationTablesService } from "./db-creation-tables.service"
import { QUERIES } from "../constants"

@Injectable({
  providedIn: "root",
})
export class ContactTypeService {
  private mDb!: SQLiteDBConnection
  public databaseName: string
  private versionUpgrades = TablesUpgrades
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion

  constructor(
    private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
    private dbCreationTablesService: DbCreationTablesService,
    @Inject(QUERIES) private queries: any 
  ) {
    this.databaseName = environment.databaseNames.filter((x) => x.name.includes("tables"))[0].name
  }

  async initDatabase(mDb: SQLiteDBConnection) {
    this.mDb = mDb
    await this.createInitialData()
  }

  async initializeDatabase() {
    // create upgrade statements
    await this.sqliteService.addUpgradeStatement({
      database: this.databaseName,
      upgrade: this.versionUpgrades,
    })

    // create and/or open the database
    await this.openDatabase()
    this.dbVerService.set(this.databaseName, this.loadToVersion)

    const isData = await this.mDb.query("select * from sqlite_sequence")

    // create database initial data
    if (isData.values!.length === 0) {
      await this.createInitialData()
    }

    if (this.sqliteService.platform === "web") {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName)
    }
  }

  async openDatabase() {
    if (
      (this.sqliteService.native || this.sqliteService.platform === "electron") &&
      (await this.sqliteService.isInConfigEncryption()).result &&
      (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result
    ) {
      this.mDb = await this.sqliteService.openDatabase(this.databaseName, true, "secret", this.loadToVersion, false)
    } else {
      this.mDb = await this.sqliteService.openDatabase(
        this.databaseName,
        false,
        "no-encryption",
        this.loadToVersion,
        false,
      )
    }
  }

  private async createInitialData(): Promise<void> {}

  getAllContactTypes(): Observable<ContactType[]> {
    return new Observable((observer: Observer<ContactType[]>) => {
      if (!this.mDb) {
        this.initializeDatabase()
          .then(() => {
            this.executeGetAllContactTypes(observer)
          })
          .catch((error) => {
            observer.error(error)
          })
      } else {
        this.executeGetAllContactTypes(observer)
      }
    })
  }

  private executeGetAllContactTypes(observer: Observer<ContactType[]>) {
    const selectQuery = this.queries.SELECT_CONTACT_TYPES || "SELECT * FROM contact_type ORDER BY name"
    this.mDb.query(selectQuery).then(
      (result: any) => {
        const contactTypes = result.values as ContactType[]
        observer.next(contactTypes)
        observer.complete()
        console.log("🔍 ContactTypes Query result:", result)
      },
      (error: any) => {
        observer.error(error)
      },
    )
  }
}
