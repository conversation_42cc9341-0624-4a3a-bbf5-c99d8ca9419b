import { Injectable } from '@angular/core';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { SQLiteService } from './sqlite.service';
import { enLanguage } from '../models/enLanguage';
import { CommonService } from './common-service.service';
import { TablesUpgrades } from '../upgrades/tables/tables';
import { environment } from 'src/environments/environment';
import { DbnameVersionService } from './dbname-version.service';
import { activitie, establishment, localitie, potential, product, sector, specialitie } from '../models/planning';

@Injectable({
  providedIn: 'root'
})
export class FilterService {
  private mDb!: SQLiteDBConnection;
  private versionUpgrades = TablesUpgrades;
  public databaseName: string;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;

  sectorSelected: any;
  localitySelected: any;
  establishmentSelected: any;
  specialitySelected: any;
  potentialSelected: any;
  activitySelected: any;
  productSelected: any;
  LABELS = enLanguage.LABELS;

  constructor(
    private sqliteService: SQLiteService,
    private commonService: CommonService,
    private dbVerService: DbnameVersionService
  ) {
    this.databaseName = environment.databaseNames.find(x => x.name.includes('tables'))?.name || 'default';

  }

  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;

}
  /*//////// connection avec la bdd//////////////////
  async initializeDatabase() {
    try {
      // create upgrade statements
      await this.sqliteService.addUpgradeStatement({
        database: this.databaseName,
        upgrade: this.versionUpgrades
      });
      // create and/or open the database
      await this.openDatabase();
      this.dbVerService.set(this.databaseName, this.loadToVersion);
      const isData = await this.mDb.query("select * from sqlite_sequence");
      
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
    } catch (error) {
      console.error('Error initializing database', error);
    }
  }

  async openDatabase() {
    try {
      if ((this.sqliteService.native || this.sqliteService.platform === "electron")
        && (await this.sqliteService.isInConfigEncryption()).result
        && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
        this.mDb = await this.sqliteService
          .openDatabase(this.databaseName, true, "secret",
            this.loadToVersion, false);
      } else {
        this.mDb = await this.sqliteService
          .openDatabase(this.databaseName, false, "no-encryption",
            this.loadToVersion, false);
      }
    } catch (error) {
      console.error('Error opening database', error);
    }
  }*/

  async getAllSpecialities(restorePreviousChoose: boolean): Promise<any[]> {
    const specialities: { id: number | null; name: string }[] = [];
    try {
       // Ensure database is open
      const result = await this.mDb.query('SELECT * FROM speciality');
      for (const value of result.values!) {
        const element = {
          id: value.id.toString(),
          name: value.name,
          action: value.action
        };
        specialities.push(element);
        if (restorePreviousChoose && localStorage.getItem('selected_speciality') === element.id) {
          this.specialitySelected = element;
        }
      }
    } catch (error) {
      console.error('Error fetching specialities', error);
    }
    return specialities;
  }

  async getAllPotential(restorePreviousChoose: boolean): Promise<any[]> {
    const potentials: { id: number| null; name: string }[] = [];
    try {
       // Ensure database is open
      const result = await this.mDb.query('SELECT * FROM potential ORDER BY name');
      for (const value of result.values!) {
        const element = {
          id: value.id.toString(),
          name: value.name
        };
        potentials.push(element);
        if (restorePreviousChoose && localStorage.getItem('selected_potential') === element.id) {
          this.potentialSelected = element;
        }
      }
    } catch (error) {
      console.error('Error fetching potentials', error);
    }
    return potentials;
  }

  async getAllActivities(restorePreviousChoose: boolean): Promise<any[]> {
    const activities: { id: number | string; name: string }[] = [];
    const allActivities = this.commonService.getAllActivities();
    if (allActivities.length > 0) {
      for (const activity of allActivities) {
        const element = {
          id: activity.id,
          name: activity.value
        };
        activities.push(element);
        if (restorePreviousChoose && localStorage.getItem('selected_activity') === element.id) {
          this.activitySelected = element;
        }
      }
    }
    return activities;
  }
  async getAllLocalities(restorePreviousChoose: boolean): Promise<Array<{ id: number | null; name: string }>> {
    const localities: Array<{ id: number | null; name: string }> = [];
    try {
     
      const result = await this.mDb.query(
        'SELECT * FROM locality ORDER BY name',
        []
      );
      for (const row of result.values!) {
        const element = {
          id: row.id as number,
          name: row.name as string
        };
        localities.push(element);
  
        // if requested, restore the last selection
        if (
          restorePreviousChoose &&
          localStorage.getItem('selected_locality') === String(element.id)
        ) {
          this.localitySelected = element;
        }
      }
    } catch (error) {
      console.error('Error fetching localities', error);
    }
    return localities;
  }

  async getAllSectors(restorePreviousChoose: boolean): Promise<any[]> {
    const sectors: { id: number | null; name: string }[] = [];
    try {
     // Ensure database is open
      const result = await this.mDb.query('SELECT * FROM sector ORDER BY name');
      for (const value of result.values!) {
        const element = {
          id: value.id.toString(),
          name: value.name
        };
        sectors.push(element);
        if (restorePreviousChoose && localStorage.getItem('selected_sector') === element.id) {
          this.sectorSelected = element;
        }
      }
    } catch (error) {
      console.error('Error fetching sectors', error);
    }
    return sectors;
  }

  async getAllEstablishments(restorePreviousChoose: boolean): Promise<any[]> {
    const establishments: { id: number| null; name: string }[] = [];
    try {
      // Ensure database is open
      const result = await this.mDb.query('SELECT * FROM establishment');
      for (const value of result.values!) {
        const element = {
          id: value.id.toString(),
          name: value.name
        };
        establishments.push(element);
        if (restorePreviousChoose && localStorage.getItem('selected_establishment') === element.id) {
          this.establishmentSelected = element;
        }
      }
    } catch (error) {
      console.error('Error fetching establishments', error);
    }
    return establishments;
  }

  async filterLocalitiesBySector(sectorId: number): Promise<any[]> {
    const localities: { id: number | null; name: string }[] = [];
    localities.push({ id: null, name: enLanguage.LABELS.ALL });
    if (sectorId !== null) {
      
      try {
         // Ensure database is open
        //Log the query and parameter
        console.log('Executing query:', 'SELECT * FROM locality WHERE sectorId = ?', 'with parameter:', sectorId);
        
        const result = await this.mDb.query('SELECT * from locality where sector_id = ? order by name ', [sectorId]);
        console.log("DB result",result)
        for (const value of result.values!) {
          const element = {
            id: value.id,
            name: value.name
          };
          localities.push(element);
          if (localStorage.getItem('selected_locality') === element.id) {
            this.localitySelected = element;
          }
        }
        console.log("localities ",localities)
      } catch (error) {
        console.error('Error fetching localities', error);
      }
    }
    else{
      console.log('Executing query:', 'SELECT * FROM locality ', 'with parameter:', sectorId);
        
      const result = await this.mDb.query('SELECT * from locality order by name ', [sectorId]);
      console.log("DB result",result)
      for (const value of result.values!) {
        const element = {
          id: value.id,
          name: value.name
        };
        localities.push(element);
        if (localStorage.getItem('selected_locality') === element.id) {
          this.localitySelected = element;
        }
      }
      console.log("localities ",localities)

    }
    return localities;
  }
  
  async getSectorByLocality(localityId: number): Promise<{ id: number|null; name: string }> {
    // start with the “All” option
    const defaultSector = { id: null, name: enLanguage.LABELS.ALL };
    try {
      const sql = `
        SELECT s.id, s.name
          FROM sector s
          JOIN locality l ON l.sector_id = s.id
         WHERE l.id = ?
         LIMIT 1
      `;
      console.log('Executing query:', sql, 'with localityId =', localityId);
      const result = await this.mDb.query(sql, [ localityId ]);
      if (result.values && result.values.length > 0) {
        const row: any = result.values[0];
        return { id: row.id, name: row.name };
      }
    } catch (e) {
      console.error('getSectorByLocality error', e);
    }
    return defaultSector;
  }

  async filterEstablishmentsByActivity(activityId: string): Promise<any[]> {
    const establishments: { id: number | null; name: string }[] =[];
    if (activityId === null) {
      return this.getAllEstablishments(false);
    } else {
      establishments.push({ id: null, name: enLanguage.LABELS.ALL });
      try {
         // Ensure database is open
         // Log the query and parameter
         console.log('Executing query:', 'SELECT * FROM establishment WHERE activity = ?', 'with parameter:', activityId);
        
        const result = await this.mDb.query('SELECT * FROM establishment WHERE activity = ?', [activityId]);
        console.log(result)
        for (const value of result.values!) {
          const element = {
            id: value.id.toString(),
            name: value.name
          };
          establishments.push(element);
          if (localStorage.getItem('selected_establishment') === element.id) {
            this.establishmentSelected = element;
          }
        }
      } catch (error) {
        console.error('Error fetching establishments by activity', error);
      }
    }
    return establishments;
  }



}




