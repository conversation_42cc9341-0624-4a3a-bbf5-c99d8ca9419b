export const PlanningVersionUpgrades = [
    {
        toVersion: 1,
        statements: [
            `CREATE TABLE IF NOT EXISTS planningValidation (
                id integer primary key, 
                notes text, 
                status text, 
                planningValidationDate integer, 
                synchronized integer
            );`,
            `CREATE TABLE IF NOT EXISTS planning (
                id integer primary key AUTOINCREMENT, 
                planningDate integer, 
                prospectId integer, 
                synchronized integer, 
                status text
            );`,
            `CREATE TABLE IF NOT EXISTS prospect (
                id integer primary key, 
                firstname text, 
                lastname text, 
                activity text, 
                potential integer,
                address text,
                gsm text,
                phone text,
                email text,
                note text, 
                secretary text, 
                grade text, 
                specialityId integer, 
                sectorId integer, 
                localityId integer,
                lat double,
                lng double,
                mapAddress text,
                status text, 
                synchronized boolean, 
                validation integer, 
                typeId integer, 
                establishmentId integer
            );`,
            `CREATE TABLE IF NOT EXISTS product (
                id integer primary key, 
                ordre integer, 
                name text, 
                price number, 
                buyingPrice number, 
                version number, 
                quantityUnit text, 
                packageQuantity number,
                stock number, 
                description text
            );`,
            `CREATE TABLE IF NOT EXISTS prospectOrderPrediction (
                id integer primary key, 
                predictionDate integer, 
                prospectId integer, 
                productId integer, 
                orderQuantity integer
      );`,
      `CREATE TABLE IF NOT EXISTS user (
            user_id INTEGER PRIMARY KEY,
            username TEXT,
            password TEXT,
            first_last_name TEXT,
            work_type TEXT,
            working_days INTEGER,
            last_synchronisation TEXT,
            last_receive_date INTEGER,
            first_sync BOOLEAN,
            time TEXT,
            auto_sync BOOLEAN,
            lock_after_sync BOOLEAN,
            multi_wholesaler BOOLEAN,
            sync_cycle NUMBER,
            comments_dictionary TEXT,
            open_report_period NUMBER,
            open_expense_period NUMBER
            );`,
            `CREATE TABLE IF NOT EXISTS speciality (
                id INTEGER PRIMARY KEY, 
                name TEXT, 
                action INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS prospect_type (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS sector (
                id INTEGER PRIMARY KEY, 
                name TEXT
            );`,
            `CREATE TABLE IF NOT EXISTS locality (
                id INTEGER PRIMARY KEY, 
                name TEXT, 
                sectorId INTEGER
            );`,
            `CREATE TABLE IF NOT EXISTS establishment (
                id INTEGER PRIMARY KEY AUTOINCREMENT, 
                name TEXT, 
                activity TEXT
            );`
           
        ]
    }
];
