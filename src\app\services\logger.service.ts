import { Injectable } from "@angular/core"
import { LogLevel } from "src/app/models/logger"
import  { Storage } from "@ionic/storage-angular"

export interface LogEntry {
  level: string
  message: string
  timestamp: string
  userId?: number
}

@Injectable({
  providedIn: "root",
})
export class LoggerService {
  logLevel: LogLevel = new LogLevel()
  storageFilename!: string
  private storageReady: Promise<void>
  private currentUserId?: number
  private readonly LOG_STORAGE_KEY = "app_logs"
  private readonly MAX_LOGS = 1000 // Limite du nombre de logs à garder

  constructor(private storage: Storage) {
    this.storageReady = this.initializeStorage()
  }

  private async initializeStorage(): Promise<void> {
    await this.storage.create()
  }

  setStorageFilename(filename: string): void {
    this.storageFilename = filename
  }

  setUserId(userId: number): void {
    this.currentUserId = userId
  }

  info(msg: string): void {
    this.logWith(this.logLevel.Info, "INFO", msg)
  }

  warn(msg: string): void {
    this.logWith(this.logLevel.Warn, "WARN", msg)
  }

  error(msg: string, error?: unknown): void {
    const errorMsg = error ? `${msg} - ${JSON.stringify(error)}` : msg
    this.logWith(this.logLevel.Error, "ERROR", errorMsg)
  }

  private logWith(level: any, levelName: string, msg: string): void {
    if (level <= this.logLevel.Error) {
      // Afficher dans la console comme avant
      switch (level) {
        case this.logLevel.None:
          console.log(msg)
          break
        case this.logLevel.Info:
          console.info("%c" + msg, "color: #6495ED")
          break
        case this.logLevel.Warn:
          console.warn("%c" + msg, "color: #FF8C00")
          break
        case this.logLevel.Error:
          console.error("%c" + msg, "color: #DC143C")
          break
        default:
          console.debug(msg)
      }

      // Sauvegarder dans le storage local
      this.saveLogToStorage(levelName, msg)
    }
  }

  private async saveLogToStorage(level: string, message: string): Promise<void> {
    try {
      await this.storageReady

      const logEntry: LogEntry = {
        level: level,
        message: message,
        timestamp: new Date().toISOString(),
        userId: this.currentUserId,
      }

      // Récupérer les logs existants
      const existingLogs = await this.getAllLogsFromStorage()

      // Ajouter le nouveau log
      existingLogs.push(logEntry)

      // Limiter le nombre de logs (garder seulement les plus récents)
      if (existingLogs.length > this.MAX_LOGS) {
        existingLogs.splice(0, existingLogs.length - this.MAX_LOGS)
      }

      // Sauvegarder dans le storage
      await this.storage.set(this.LOG_STORAGE_KEY, existingLogs)
    } catch (error) {
      console.error("Error saving log to storage:", error)
    }
  }

  private async getAllLogsFromStorage(): Promise<LogEntry[]> {
    try {
      await this.storageReady
      const logs = await this.storage.get(this.LOG_STORAGE_KEY)
      return logs || []
    } catch (error) {
      console.error("Error retrieving logs from storage:", error)
      return []
    }
  }

  // Récupérer tous les logs pour l'envoi
  async getAllLogs(): Promise<LogEntry[]> {
    return await this.getAllLogsFromStorage()
  }

  // Récupérer les logs depuis une date
  async getLogsSince(since: string): Promise<LogEntry[]> {
    const allLogs = await this.getAllLogsFromStorage()
    return allLogs.filter((log) => log.timestamp > since)
  }

  // Supprimer les anciens logs (garder seulement les X derniers jours)
  async cleanOldLogs(daysToKeep = 7): Promise<void> {
    try {
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
      const cutoffIso = cutoffDate.toISOString()

      const allLogs = await this.getAllLogsFromStorage()
      const recentLogs = allLogs.filter((log) => log.timestamp > cutoffIso)

      await this.storage.set(this.LOG_STORAGE_KEY, recentLogs)
      console.log(`Cleaned logs older than ${daysToKeep} days`)
    } catch (error) {
      console.error("Error cleaning old logs:", error)
    }
  }

  // Supprimer tous les logs
  async clearAllLogs(): Promise<void> {
    try {
      await this.storage.remove(this.LOG_STORAGE_KEY)
      console.log("All logs cleared")
    } catch (error) {
      console.error("Error clearing logs:", error)
    }
  }

  // Formater les logs pour l'envoi au serveur
  async getLogsForUpload(): Promise<string> {
    const logs = await this.getAllLogs()

    if (logs.length === 0) {
      return ""
    }

    return logs.map((log) => `[${log.timestamp}] [${log.level}] ${log.message}`).join("\n")
  }

  // Obtenir le nombre de logs stockés
  async getLogsCount(): Promise<number> {
    const logs = await this.getAllLogsFromStorage()
    return logs.length
  }

  // Obtenir les logs par niveau
  async getLogsByLevel(level: string): Promise<LogEntry[]> {
    const allLogs = await this.getAllLogsFromStorage()
    return allLogs.filter((log) => log.level === level)
  }
}
