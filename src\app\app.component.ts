import { Component, OnInit } from '@angular/core';
import { TranslationService } from './services/traduction-service.service';
import { ThemeService } from './services/theme.service';
import { ConsoleService } from './services/console-service.service';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
})
export class AppComponent implements OnInit {
  buttonLabel: string = 'Dark Mode';
  constructor(private translationService: TranslationService,
    private themeService: ThemeService,
    private consoleService: ConsoleService
  ) {}

  ngOnInit() {
    this.translationService.loadTranslations('an.json').subscribe();
    console.log('Application démarrée');
  }

  changeLanguage(language: string) {
    this.translationService.loadTranslations(language).subscribe();
    document.documentElement.dir = language === 'ar.json' ? 'rtl' : 'ltr';
  }

  translate(key: string): string {
    return this.translationService.translate(key);
  }
  getButtonLabel(): string {
    if (this.themeService.theme === 'light-theme') {
      return 'Light Mode';
    }
    return 'Dark Mode';
  }
  switchTheme(): void {
    this.themeService.switchTheme();
  }
}



  
