import { Injectable } from '@angular/core';
import { LoggerService } from './logger.service';
import { SQLiteService } from './sqlite.service';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { Directory, Encoding, Filesystem } from '@capacitor/filesystem';
import { DbCreationTablesService } from './db-creation-tables.service';
import { ConsoleService } from './console-service.service';
@Injectable({
  providedIn: 'root'
})
export class TroubleShootingService {
  private db = this.dbCreationTablesService.getDatabase();;
  private showConsole: boolean = false;

  constructor(
    private logger: LoggerService,
    private sqliteService: SQLiteService,
    private dbCreationTablesService: DbCreationTablesService,
    private consoleService: ConsoleService
  ) {
    // Initialisation de la base de données de manière asynchrone
  
  }

  private async initializeDatabase(): Promise<void> {
    try {
      await this.sqliteService.initializePlugin();
      this.db =   await this.sqliteService.openDatabase('starter_tables.db', false, 'no-encryption', 1, false);
    } catch (error) {
      this.logger.error('Error initializing database', error);
      throw error; // Propagation de l'erreur pour gestion éventuelle
    }
  }

  async executeRequest(request: string): Promise<any[]> {
    try {
      const res = await this.db.query(request);
      return res.values || [];
    } catch (error: any) {
      const errorMessage = error.message || 'Unknown error';
      const errorStack = error.stack || '';

      this.logger.error('Error executing request: ' + errorMessage, {
        stack: errorStack,
        request
      });

      return [];
    }
  }

  async exportDatabase(): Promise<void> {
    try {
      // Étape 1 : Obtenir tous les noms de tables
      const result = await this.db.query("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%';");

      // Vérifier si le résultat contient des valeurs
      const tables = result.values?.map(row => row.name) || [];

      // Étape 2 : Créer l'objet d'exportation JSON
      const exportObject: Record<string, any[]> = {}; // Définir un type pour l'objet exporté
      for (const table of tables) {
        // Pour chaque table, obtenir toutes les données
        const tableData = await this.db.query(`SELECT * FROM ${table}`);
        exportObject[table] = tableData.values || [];
      }

      // Étape 3 : Convertir l'objet en JSON
      const jsonString = JSON.stringify(exportObject);

      // Étape 4 : Écrire le fichier JSON dans le système de fichiers
      await Filesystem.writeFile({
        path: 'DataBase.json',
        data: jsonString,
        directory: Directory.Documents,
        encoding: Encoding.UTF8
      });

      this.logger.info('Database exported successfully');
    } catch (error: any) {
      this.logger.error('Error exporting database', error);
      throw error; // Propagation de l'erreur pour gestion éventuelle
    }
  }

  async getShowConsole(): Promise<boolean> {
    return this.consoleService.getConsoleVisibility();
  }

  async setShowConsole(showConsole: boolean): Promise<void> {
    this.consoleService.setConsoleVisibility(showConsole);
  }
  
}