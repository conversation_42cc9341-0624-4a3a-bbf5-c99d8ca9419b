import { Injectable } from '@angular/core';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { SQLiteService } from './sqlite.service';
import { DbnameVersionService } from './dbname-version.service';
import { environment } from 'src/environments/environment';
import { Observable, Observer } from 'rxjs';
import { Expense, ExpenseType } from '../models/expense';
import { TablesUpgrades } from '../upgrades/tables/tables';

@Injectable({
  providedIn: 'root'
})
export class ExpenseService {
//variables
private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;
  total: number = 0;

  constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }

  async initDatabase(mDb: SQLiteDBConnection){
      this.mDb = mDb;
      await this.createInitialData();
  }
  async initializeDatabase() {
    // create upgrade statements
    await this.sqliteService
      .addUpgradeStatement({
        database: this.databaseName,
        upgrade: this.versionUpgrades
      });
    // create and/or open the database
    await this.openDatabase();
    this.dbVerService.set(this.databaseName, this.loadToVersion);
    const isData = await this.mDb.query("select * from sqlite_sequence");
    // create database initial data
    if (isData.values!.length === 0) {
      await this.createInitialData();
    }
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  }
  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, true, "secret",
          this.loadToVersion, false);

    } else {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, false, "no-encryption",
          this.loadToVersion, false);
    }
  }
  private async createInitialData(): Promise<void> {
    // create 
   /* for (const expense of MOCK_EXPENSE) {
      await this.getExpense(expense);
    }
    for (const type of MOCK_EXPENSE_TYPE) {
      await this.getExpenseType(type);
    }*/
  }
  //////// expense//////////////////

  getAllExpenses(): Observable<{ expenses: Expense[], total: number }> {
    const selectQuery = 'SELECT * FROM expense;';

    return new Observable((observer: Observer<{ expenses: Expense[], total: number }>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const values = this.sqliteService.snakeToCamel(result.values)
          const expenses = values as Expense[];
          const total = this.sumProperty(expenses, 'montant');

          observer.next({ expenses, total });
          observer.complete();
        },
        (error: any) => {
          observer.error(error);
        }
      );
    });
  }

  sumProperty<T>(array: T[], prop: keyof T): number {
    return array.reduce((acc, curr) => acc + (curr[prop] as unknown as number), 0);
  }
  async getExpense(jsonExpense: Expense): Promise<Expense> {
    let expense = await this.sqliteService.findOneBy(this.mDb, "expense", { id: jsonExpense.id });
    if (!expense) {

      if (jsonExpense.expenseDate) {
        // create a new expense
        let expense = new Expense();
        expense.id = jsonExpense.id;
        expense.expenseDate = jsonExpense.expenseDate;
        expense.expenseTypeId = jsonExpense.expenseTypeId;
        expense.activityId = jsonExpense.activityId;
        expense.montant = jsonExpense.montant;
        expense.status = jsonExpense.status;
        expense.synchronized = jsonExpense.synchronized

        if (jsonExpense.attachementBase64) {
          expense.attachementBase64 = jsonExpense.attachementBase64;
        }
        if (jsonExpense.attachmentName) {
          expense.attachmentName = jsonExpense.attachmentName;
        }
        if (jsonExpense.description) {
          expense.description = jsonExpense.description;
        }
        expense=this.sqliteService.camelToSnake(expense)

        await this.sqliteService.save(this.mDb, "expense", expense);
        
        this.sqliteService.sqliteConnection.saveToStore(this.databaseName);

        // cette etape pour verifier que l'element est bien sauvgarde dans la table 
        expense = await this.sqliteService.findOneBy(this.mDb, "expense", { id: jsonExpense.id });
        if (expense) {
          return expense;
        } else {
          return Promise.reject(`failed to getExpense for id ${jsonExpense.id}`);
        }
      } else {
        // expense not in the database
        let expense = new Expense();
        expense.id = -1;
        return expense;
      }
    } else {
      return expense;
    }

  }
  deleteExpense(id: number): Observable<void> {

    return new Observable<void>((observer) => {
      this.sqliteService.remove(this.mDb, "expense", { id: id })
        .then(() => {
          return this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
        })
        .then(() => {
          observer.next();
          observer.complete();
        })
        .catch(error => {
          console.error('Error deleting expense:', error);
          observer.error(error);
        });
    });
  }
  //////// Types//////////////////

  async getExpenseType(jsonExpenseType: ExpenseType): Promise<ExpenseType> {
    let type = await this.sqliteService.findOneBy(this.mDb, "expense_type", { id: jsonExpenseType.id });

    if (!type) {

      if (1) {
        let type = new ExpenseType();
        type.id = jsonExpenseType.id;
        type.amount = jsonExpenseType.amount;
        type.name = jsonExpenseType.name;
        type.requiredAttachment = jsonExpenseType.requiredAttachment;
        type=this.sqliteService.camelToSnake(type)
        await this.sqliteService.save(this.mDb, "expense_type", type);
        // cette etape pour verifier que l'element est bien sauvgarde dans la table 
        type = await this.sqliteService.findOneBy(this.mDb, "expense_type", { id: jsonExpenseType.id });
        if (type) {
          return type;
        } else {
          return Promise.reject(`failed to getExpenseType for id ${jsonExpenseType.id}`);
        }
      } else {
        // expense not in the database
        let type = new ExpenseType();
        type.id = -1;
        return type;
      }
    } else {
      return type;
    }

  }
  getAllExpenseTypes(): Observable<ExpenseType[]> {
    const selectQuery = 'SELECT * FROM expense_type;';

    return new Observable((observer: Observer<ExpenseType[]>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const values = this.sqliteService.snakeToCamel(result.values)
          const types = values as ExpenseType[];
          observer.next(types);
          observer.complete();
        },
        (error: any) => {
          observer.error(error);
        }
      );
    });
  }
  /// update 
  async updateExpense(expense: any): Promise<Expense> {
    expense=this.sqliteService.camelToSnake(expense)
    await this.sqliteService.save(this.mDb, 'expense', expense, { id: expense.id });
    this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    const result = await this.sqliteService.findOneBy(this.mDb, 'expense', { id: expense.id });
    if (result) {
      return result;
    } else {
      return Promise.reject(`failed to updateExpense for id ${expense.id}`);
    }
  }
   // filtre 
  getExpenseByDates(firstDate: Date, lastDate: Date): Observable<Expense[]> {
    const selectQuery = 'SELECT * FROM expense WHERE expense_date BETWEEN ? AND ?';
    return new Observable((observer) => {
      this.mDb.query(selectQuery, [firstDate.getTime(), lastDate.getTime()]).then(result => {
        const values = this.sqliteService.snakeToCamel(result.values)
        const expenses = values as Expense[]
        console.log (expenses );
        observer.next(expenses);
        observer.complete();
      }).catch(error => observer.error(error));
    });
  }

  async getExpenseByDay(currentDate: Date) {
    const selectQuery = `
         SELECT * FROM expense WHERE (expense_date = ?) AND (status<>'DELETED')  ORDER BY expense_date 
        `;
    const res = await this.mDb.query(selectQuery,[currentDate.setHours(0,0,0,0)]);
    const values = this.sqliteService.snakeToCamel(res.values)
    return values || [];
  }

  
}
