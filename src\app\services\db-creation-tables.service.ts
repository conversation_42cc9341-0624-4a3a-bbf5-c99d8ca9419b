import { Injectable } from "@angular/core"
import  { SQLiteService } from "./sqlite.service"
import { TablesUpgrades } from "src/app/upgrades/tables/tables"
import  { DbnameVersionService } from "./dbname-version.service"
import  { SQLiteDBConnection } from "@capacitor-community/sqlite"
import { environment } from "src/environments/environment"

@Injectable({
  providedIn: "root",
})
export class DbCreationTablesService {
  private mDb!: SQLiteDBConnection
  public databaseName: string
  private versionUpgrades = TablesUpgrades
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion

  constructor(
    private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
  ) {
    this.databaseName = environment.databaseNames.filter((x) => x.name.includes("tables"))[0].name
  }

  async initializeDatabase() {
    if (this.sqliteService.platform === "web") {
      // Vérifie si la base est déjà stockée dans IndexedDB
      const dbExists = await this.sqliteService.sqliteConnection.isDatabase(this.databaseName)
      if (dbExists.result) {
        console.log(`🔄 La base de données '${this.databaseName}' existe déjà, vérification des migrations.`)
        await this.openDatabase()
        await this.checkAndRunMigrations()
        return
      }
    }

    console.log(`🆕 Création de la base de données '${this.databaseName}'`)

    await this.sqliteService.addUpgradeStatement({
      database: this.databaseName,
      upgrade: this.versionUpgrades,
    })

    await this.openDatabase()
    this.dbVerService.set(this.databaseName, this.loadToVersion)

    const isData = await this.mDb.query("SELECT * FROM sqlite_sequence")

    if (isData.values!.length === 0) {
      await this.createInitialData()
    }

    // Sauvegarde la base dans IndexedDB après l'initialisation
    if (this.sqliteService.platform === "web") {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName)
      console.log("✅ Base sauvegardée dans IndexedDB")
    }
  }

  private async checkAndRunMigrations() {
    try {
      // Récupérer la version actuelle de la base de données
      const currentVersion = this.dbVerService.getVersion(this.databaseName)

      console.log(`📊 Version actuelle: ${currentVersion}, Version cible: ${this.loadToVersion}`)

      if (currentVersion < this.loadToVersion) {
        console.log(`🔄 Migration nécessaire de la version ${currentVersion} vers ${this.loadToVersion}`)
        await this.runMigrations(currentVersion, this.loadToVersion)
      } else {
        console.log(`✅ Base de données à jour (version ${currentVersion})`)
      }
    } catch (error) {
      console.error("❌ Erreur lors de la vérification des migrations:", error)
    }
  }

  private async runMigrations(fromVersion: number, toVersion: number) {
    try {
      // Filtrer les upgrades nécessaires (versions supérieures à la version actuelle)
      const migrationsToRun = this.versionUpgrades.filter(
        (upgrade) => upgrade.toVersion > fromVersion && upgrade.toVersion <= toVersion,
      )

      console.log(`🚀 Exécution de ${migrationsToRun.length} migration(s)`)

      for (const migration of migrationsToRun) {
        console.log(`⬆️ Migration vers la version ${migration.toVersion}`)

        // Exécuter chaque statement de la migration
        for (const statement of migration.statements) {
          try {
            await this.mDb.execute(statement)
            console.log(`✅ Statement exécuté: ${statement.substring(0, 50)}...`)
          } catch (error) {
            console.error(`❌ Erreur lors de l'exécution du statement: ${statement}`, error)
            throw error
          }
        }

        // Mettre à jour la version dans le service
        this.dbVerService.set(this.databaseName, migration.toVersion)
        console.log(`✅ Version mise à jour: ${migration.toVersion}`)
      }

      // Sauvegarder la base après migration (pour le web)
      if (this.sqliteService.platform === "web") {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName)
        console.log("💾 Base sauvegardée après migration")
      }

      console.log(`🎉 Migration terminée avec succès vers la version ${toVersion}`)
    } catch (error) {
      console.error("❌ Erreur lors de la migration:", error)
      throw error
    }
  }

  async openDatabase() {
    if (
      (this.sqliteService.native || this.sqliteService.platform === "electron") &&
      (await this.sqliteService.isInConfigEncryption()).result &&
      (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result
    ) {
      this.mDb = await this.sqliteService.openDatabase(this.databaseName, true, "secret", this.loadToVersion, false)
    } else {
      this.mDb = await this.sqliteService.openDatabase(
        this.databaseName,
        false,
        "no-encryption",
        this.loadToVersion,
        false,
      )
    }
  }

  private async createInitialData(): Promise<void> {
    // Ajouter ici les instructions pour insérer les données initiales dans la base de données
    // Exemple :
    // await this.mDb.execute('INSERT INTO my_table (column1, column2) VALUES (?, ?)', [value1, value2]);
  }

  public getDatabase(): SQLiteDBConnection {
    return this.mDb
  }
}
