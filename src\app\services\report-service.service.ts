import { Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { DateService } from './date.service';
import { CommonService } from "./common-service.service";
import { LoadingController } from '@ionic/angular';
import { LoggerService } from 'src/app/services/logger.service';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { DbnameVersionService } from './dbname-version.service';
import { environment } from 'src/environments/environment';
import { TablesUpgrades } from 'src/app/upgrades/tables/tables';

import { Observable, from, Observer } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class reportService {

  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;

  constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }

  // Initialize the database connection
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
    await this.createInitialData();
}
async initializeDatabase() {
  await this.sqliteService.addUpgradeStatement({
    database: this.databaseName,
    upgrade: this.versionUpgrades
  });
  await this.openDatabase();
  this.dbVerService.set(this.databaseName, this.loadToVersion);
  const isData = await this.mDb.query("SELECT * FROM sqlite_sequence");
  if (isData.values!.length === 0) {
    await this.createInitialData();
  }
  if (this.sqliteService.platform === 'web') {
    await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
  }
}

async openDatabase() {
  if ((this.sqliteService.native || this.sqliteService.platform === "electron")
    && (await this.sqliteService.isInConfigEncryption()).result
    && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
    this.mDb = await this.sqliteService.openDatabase(this.databaseName, true, "secret", this.loadToVersion, false);
  } else {
    this.mDb = await this.sqliteService.openDatabase(this.databaseName, false, "no-encryption", this.loadToVersion, false);
  }
}

private async createInitialData(): Promise<void> {
  
}
}