import { Injectable } from "@angular/core"

@Injectable({
  providedIn: "root",
})
export class DbnameVersionService {
  private _dbNameVersionDict: Map<string, number> = new Map()

  constructor() {}

  set(dbName: string, version: number) {
    this._dbNameVersionDict.set(dbName, version)
    // Optionnel: sauvegarder dans le localStorage pour persistance
    localStorage.setItem(`db_version_${dbName}`, version.toString())
  }

  getVersion(dbName: string): number {
    if (this._dbNameVersionDict.has(dbName)) {
      return this._dbNameVersionDict.get(dbName)!
    } else {
      // Essayer de récupérer depuis le localStorage
      const savedVersion = localStorage.getItem(`db_version_${dbName}`)
      if (savedVersion) {
        const version = Number.parseInt(savedVersion, 10)
        this._dbNameVersionDict.set(dbName, version)
        return version
      }
      return -1
    }
  }

  clearVersion(dbName: string) {
    this._dbNameVersionDict.delete(dbName)
    localStorage.removeItem(`db_version_${dbName}`)
  }
}
