import { Injectable } from "@angular/core"
import  { HttpClient } from "@angular/common/http"
import  { Router } from "@angular/router"
import  { SQLiteService } from "../services/sqlite.service"
import  { Storage } from "@ionic/storage-angular"
import { UserService } from "src/app/services/user-service.service"
import  { SQLiteDBConnection } from "@capacitor-community/sqlite"
import { AuthResponse } from "src/app/models/auth-response"
import { DbCreationTablesService } from './db-creation-tables.service';
import { environment } from "src/environments/environment"
import { LoggerService } from "./logger.service"
import { Directory, Encoding, Filesystem } from "@capacitor/filesystem"
@Injectable({
  providedIn: "root",
})
export class LoginService {
  private mDb!: SQLiteDBConnection;
  private storageReady: Promise<void>
  private baseUrl: string | null = null
  private laboConfig: { [key: string]: string } = {}
  private databaseName: string;

  constructor(
    private http: HttpClient,
    private sqliteService: SQLiteService,
    private storage: Storage,
    private router: Router,
    private userService: UserService,
    private dbCreationTablesService: DbCreationTablesService,
    private loggerService: LoggerService,
  ) {
    this.storageReady = this.initializeStorage()
    this.loadLaboConfig()
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  
  }

  async initDatabase(mDb: SQLiteDBConnection) {
    this.mDb = mDb
    await this.loadBaseUrlFromDb()
  }

  private async initializeStorage(): Promise<void> {
    await this.storage.create()
  }
  private async loadBaseUrlFromDb(): Promise<void> {
    try {
      const config = await this.getCurrentLaboConfig()
      if (config && config.serverUrl) {
        this.baseUrl = config.serverUrl
        console.log(`Base URL loaded from DB: ${this.baseUrl}`)
      } else {
        // If no config in DB, baseUrl remains null, indicating an unconfigured state.
        this.baseUrl = null
        console.log("No labo config found in DB. Base URL is not set.")
      }
    } catch (error) {
      console.error("Error loading base URL from database:", error)
      this.baseUrl = null // Ensure it's null on error
    }
  }
  // Charger la configuration des labos depuis le serveur
  private async loadLaboConfig() {
    try {
      const response = await this.http
        .get<{ [key: string]: string }>("https://servers.bird-notes.com/labo-url.json")
        .toPromise()
      if (response) {
        this.laboConfig = response
      }
    } catch (error) {
      console.error("Error loading labo configuration:", error)
    }
  }
  // Sauvegarder la configuration du labo dans la base de données
  private async saveLaboToDatabase(laboName: string, serverUrl: string) {
    try {
      const upsertQuery = `INSERT OR REPLACE INTO configuration (id, labo_name, server_url) VALUES (1, '${laboName}', '${serverUrl}')`;
      await this.mDb.execute(upsertQuery);
  
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
  
      console.log(`Saved labo configuration: ${laboName} -> ${serverUrl}`);
    } catch (error) {
      console.error("Error saving labo to database:", error);
      throw error;
    }
  }

  // Obtenir la configuration actuelle du labo depuis la base de données
  async getCurrentLaboConfig(): Promise<{ laboName: string; serverUrl: string } | null> {
    try {
      const result = await this.mDb.query("SELECT labo_name, server_url FROM configuration ORDER BY id DESC LIMIT 1")

      if (result.values && result.values.length > 0) {
        const config = result.values[0]
        return {
          laboName: config.labo_name,
          serverUrl: config.server_url,
        }
      }
      return null
    } catch (error) {
      console.error("Error getting current labo config:", error)
      return null
    }
  }

  async verifyTableStructure() {
    const userResult = await this.mDb.query("PRAGMA table_info(user);")
    const configResult = await this.mDb.query("PRAGMA table_info(configuration);")
    console.log("User table info:", userResult)
    console.log("Configuration table info:", configResult)
  }

  async getStoredCredentials() {
    try {
      await this.storageReady
      return await this.storage.get("credentials")
    } catch (error) {
      console.error("Error getting stored credentials:", error)
      throw error
    }
  }

  // Méthode pour changer dynamiquement le labo
  async updateLaboInfo(laboCode: string): Promise<boolean> {
    try {
      // Recharger la config si nécessaire
      if (Object.keys(this.laboConfig).length === 0) {
        await this.loadLaboConfig()
      }

      if (this.laboConfig[laboCode]) {
        const serverUrl = this.laboConfig[laboCode]

        // Sauvegarder dans la base de données
        await this.saveLaboToDatabase(laboCode, serverUrl)

        // Mettre à jour l'URL de base
        this.baseUrl = serverUrl

        console.log(`Labo configuration updated to: ${laboCode} -> ${serverUrl}`)
        return true
      } else {
        throw new Error("Code de laboratoire incorrect")
      }
    } catch (error) {
      console.error("Error setting labo code:", error)
      return false
    }
  }

  // Méthode pour obtenir l'URL complète pour les API calls
  private getApiUrl(endpoint: string): string {
    if (!this.baseUrl) {
      throw new Error("Base URL is not configured. Please select a labo or ensure configuration is loaded.")
    }
    const fullUrl = `${this.baseUrl}${endpoint}`
    console.log(`Building API URL: baseUrl=${this.baseUrl}, endpoint=${endpoint}, result=${fullUrl}`)
    return fullUrl
  }

  // Méthode pour tester différentes URLs d'authentification
  async authenticate(credentials: { username: string; password: string }): Promise<AuthResponse> {
    try {
      // Ensure baseUrl is set before attempting authentication
      if (!this.baseUrl) {
        throw new Error("Application is not configured. Please select a labo before attempting to log in.")
      }

      // Utiliser getApiUrl pour construire l'URL d'authentification
      const authUrl = this.getApiUrl("/authentication/authenticate")
      console.log("Authenticating with URL:", authUrl)
      const response = await this.http.post<AuthResponse>(authUrl, credentials).toPromise()
      console.log("Full authentication response:", response)
      if (response && response.token) {
        localStorage.setItem("token", response.token)
        localStorage.setItem("laboName", response.workType)
        return response
      } else {
        throw new Error("Authentication failed: Invalid token")
      }
    } catch (error: any) {
      console.error("Authentication error:", error)
      // Gestion spécifique des erreurs
      if (error.status === 403) {
        throw new Error("Accès refusé. Vérifiez vos identifiants.")
      } else if (error.status === 404) {
        throw new Error("Service d'authentification non trouvé. Vérifiez la configuration du serveur.")
      } else if (error.status === 401) {
        throw new Error("Identifiants incorrects.")
      } else if (error.status === 0) {
        throw new Error("Impossible de contacter le serveur. Vérifiez votre connexion.")
      }
      throw error
    }
  }
  private async executeSqlQueries(sqlQueries: string): Promise<void> {
    if (!sqlQueries || sqlQueries.trim() === '') {
      console.log("Aucune requête SQL à exécuter")
      return;
    }

    console.log("Exécution des requêtes SQL personnalisées:", sqlQueries)
    
    try {
      // Séparer les requêtes par point-virgule
      const queries = sqlQueries.split(';').filter(q => q.trim() !== '');
      
      for (const query of queries) {
        const trimmedQuery = query.trim();
        if (trimmedQuery) {
          try {
            console.log("Exécution de la requête:", trimmedQuery)
            await this.mDb.execute(trimmedQuery);
            console.log("Requête exécutée avec succès:", trimmedQuery)
          } catch (queryError) {
            console.error("Erreur lors de l'exécution de la requête:", trimmedQuery, queryError)
            // Continuer avec les autres requêtes même si une échoue
          }
        }
      }

      // Sauvegarder les changements si on est sur le web
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
      }
      
      console.log("Toutes les requêtes SQL personnalisées ont été traitées")
    } catch (error) {
      console.error("Erreur générale lors de l'exécution des requêtes SQL:", error)
    }
  }


  async handleLoginSuccess(userDetails: AuthResponse, credentials: { username: string; password: string }) {
    try {
      await this.storageReady
      await this.storage.set("credentials", credentials)
      this.loggerService.setUserId(userDetails.userId)
      this.loggerService.info(`User ${credentials.username} logged in successfully`)
      const userDetailsForDb = {
        user_id: userDetails.userId,
        username: credentials.username,
        password: credentials.password,
        first_last_name: userDetails.firstLastName,
        work_type: userDetails.workType,
        working_days: userDetails.workingDays,
        last_synchronisation: userDetails.lastSynchronisation || "",
        last_receive_date: userDetails.lastReceiveDate || 0,
        first_sync: userDetails.firstSync || false,
        time: userDetails.time || "",
        auto_sync: userDetails.autoSync,
        lock_after_sync: userDetails.lockAfterSync,
        multi_wholesaler: userDetails.multiWholesaler,
        sync_cycle: userDetails.syncCycle,
        comments_dictionary: userDetails.commentsDictionary,
        open_report_period: userDetails.openReportPeriod || null,
        open_expense_period: userDetails.openExpensePeriod || null,
        sql_queries_to_execute: userDetails.sqlQueriesToExecute || null,
        force_sending_log: userDetails.forceSendingLog ? 1 : 0,
      }

      this.userService.setUserId(userDetails.userId)

      const existingUser = await this.sqliteService.findOneBy(this.mDb, "user", { user_id: userDetails.userId })

      if (existingUser) {
        await this.sqliteService.save(this.mDb, "user", userDetailsForDb, { user_id: userDetails.userId })
      } else {
        await this.sqliteService.save(this.mDb, "user", userDetailsForDb)
      }

      console.log("Login successful:", userDetailsForDb)
      if (userDetails.sqlQueriesToExecute) {
        console.log("Exécution des requêtes SQL personnalisées après authentification")
        await this.executeSqlQueries(userDetails.sqlQueriesToExecute)
      }
      if (userDetails.forceSendingLog) {
        console.log("Envoi des logs au serveur suite à la connexion réussie.");
        await this.uploadLogs(userDetails.userId.toString()); // Utiliser userId comme issueId
      }
   
    } catch (error) {
      console.error("Error handling login success:", error)
      throw error
    }
  }
  async uploadLogs(issueId: string): Promise<void> {
    try {
      const logsContent = await this.loggerService.getLogsForUpload();
      if (!logsContent) {
        console.log("Aucun log à envoyer.");
        return;
      }
  
      const fileName = "app_logs.txt";
      const filePath = `${Directory.Data}/${fileName}`;

      // Écrire le contenu des logs dans un fichier temporaire
      await Filesystem.writeFile({
        path: fileName,
        data: logsContent,
        directory: Directory.Data,
        encoding: Encoding.UTF8,
      });

      // Créer un Blob à partir des données du fichier (pas besoin de relire le fichier si on a déjà le contenu)
      const blob = new Blob([logsContent], { type: "application/octet-stream" });
      
      const formData = new FormData();
      formData.append("logFile", blob, fileName); // "logFile" doit correspondre au nom attendu par votre serveur
  
      const uploadUrl = `${this.baseUrl}/authentication/upload/${issueId}`;
      console.log("Envoi des logs à l\"URL:", uploadUrl);
  
      // Envoyer le FormData avec HttpClient
      await this.http.post(uploadUrl, formData, { headers: { 'Content-Type': 'multipart/form-data' } }).toPromise();
  
      console.log("Logs envoyés avec succès.");
      await this.loggerService.clearAllLogs(); // Nettoyer les logs après envoi
    } catch (error) {
      console.error("Erreur lors de l'envoi des logs:", error);
      // Gérer l'erreur, par exemple, afficher une alerte à l'utilisateur
    }
  }


  async logout() {
    try {
      await this.storage.remove("credentials")
      localStorage.removeItem("token")
      this.router.navigate(["/login"])
    } catch (error) {
      console.error("Error during logout:", error)
    }
  }

  async getToken(): Promise<string | null> {
    try {
      return localStorage.getItem("token")
    } catch (error) {
      console.error("Error retrieving token:", error)
      return null
    }
  }

  // Getters pour accéder aux informations du labo actuel
  getCurrentLaboUrl(): string | null{
    return this.baseUrl
  }
  // Méthode pour réinitialiser la configuration du labo
  async resetLaboConfiguration(): Promise<void> {
    try {
      // Réinitialiser avec les valeurs par défaut dans la DB
      await this.mDb.execute("INSERT OR REPLACE INTO configuration (id, labo_name, server_url) VALUES (1, '', '')")
      if (this.sqliteService.platform === "web") {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName)
      }
      console.log("Labo configuration reset")
      // After resetting, baseUrl is explicitly set to null, indicating an unconfigured state.
      this.baseUrl = null
    } catch (error) {
      console.error("Error resetting labo configuration:", error)
      throw error
    }
  }

  // Méthode pour vérifier si un labo est configuré
  async isLaboConfigured(): Promise<boolean> {
    const config = await this.getCurrentLaboConfig()
    return config !== null
  }
}
