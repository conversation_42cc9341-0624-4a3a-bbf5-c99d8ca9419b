import { Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { LoggerService } from './logger.service';
import { Platform, AlertController, ModalController } from '@ionic/angular';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { environment } from 'src/environments/environment';
import { DbnameVersionService } from './dbname-version.service';
import { Observable, from, Observer } from 'rxjs';
import { TablesUpgrades } from '../upgrades/tables/tables';
import { Prospect } from '../models/planning';
import { ProspectDetailComponent } from '../components/prospect-detail/prospect-detail.component';
import { ProspectPopupComponent } from '../components/prospect-popup/prospect-popup.component';


@Injectable()
export class ProspectService {
  public mDb!: SQLiteDBConnection;
  public databaseName: string;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;
  private versionUpgrades =TablesUpgrades;
 

  constructor(
    private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
    private logger: LoggerService,
    private platform: Platform,
    private modalController: ModalController
  ) {
    this.databaseName = environment.databaseNames.find(x => x.name.includes('tables'))?.name || 'default';
  }

  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
    await this.createInitialData();
}

  private async createInitialData(): Promise<void> {
    // Initialiser les données de la base de données ici si nécessaire
  }

  getProspectNumber(): Observable<number> {
    const selectQuery = 'SELECT count(id) as count FROM prospect WHERE status <> "DELETED" AND status <> "NOT_AFFECTED"';
    return new Observable((observer: Observer<number>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const count = result.values[0].count as number;
          observer.next(count);
          observer.complete();
        },
        (error: any) => {
          this.logger.info('Error executing SQL: ' + JSON.stringify(error));
          observer.error(error);
        }
      );
    });
  }

  public async openProspectDetails(prospectId: number): Promise<any> {
    const selectQuery = `
      SELECT p.*,
             s.name   AS sectorName,
             l.name   AS localityName,
             sp.name  AS specialityName,
             pt.name  AS potentialName,
             es.name  AS establishmentName
        FROM prospect p
        INNER JOIN sector     s  ON p.sector_id      = s.id
        INNER JOIN locality   l  ON p.locality_id    = l.id
        INNER JOIN potential  pt ON p.potential      = pt.id
        INNER JOIN speciality sp ON p.speciality_id  = sp.id
        LEFT JOIN establishment es ON es.id         = p.establishment_id
       WHERE p.id = ? AND p.status <> 'NOT_AFFECTED'
    `;
    // 1) fetch the record
    const result: any = await this.mDb.query(selectQuery, [prospectId]);
    if (!result.values?.length) {
      throw new Error('No prospect found for ID ' + prospectId);
    }
    const selectedProspect = result.values[0] as Prospect;

    // 2) immediately pop up the modal
    const modal = await this.modalController.create({
      component: ProspectPopupComponent,
      componentProps: { prospect: selectedProspect }
    });
    await modal.present();

    // 3) return it in case some caller still wants the raw data
    return selectedProspect;
  }
  

  // Adjust the showProspectPopup method in the ProspectService to accept both prospectId and relatedData
async showProspectPopup( prospect: Prospect) {
  const modal = await this.modalController.create({
    component: ProspectPopupComponent,
    componentProps: { prospect: prospect }
  });
  await modal.present();
}


}  