import { Injectable } from '@angular/core';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { TablesUpgrades } from '../upgrades/tables/tables';
import { SQLiteService } from './sqlite.service';
import { DbnameVersionService } from './dbname-version.service';
import { environment } from 'src/environments/environment';
import { Planning, Prospect } from '../models/planing';
import { Observable, Observer } from 'rxjs';
import { mockPlanning, mockProspects } from '../mock-data/planing';

@Injectable({
  providedIn: 'root'
})
export class PlanningService {
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;
  constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;

}
  async initializeDatabase() {
    // create upgrade statements
    await this.sqliteService
      .addUpgradeStatement({
        database: this.databaseName,
        upgrade: this.versionUpgrades
      });
    // create and/or open the database
    await this.openDatabase();
    this.dbVerService.set(this.databaseName, this.loadToVersion);
    const isData = await this.mDb.query("select * from sqlite_sequence");
    // create database initial data
    if (isData.values!.length === 0) {
      await this.createInitialData();
    }
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  }
  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, true, "secret",
          this.loadToVersion, false);

    } else {
      this.mDb = await this.sqliteService
        .openDatabase(this.databaseName, false, "no-encryption",
          this.loadToVersion, false);
    }
  }
  private async createInitialData(): Promise<void> {
  
  }
  // planning 
  getAllPlannings(): Observable<Planning[]> {
    const selectQuery = 'SELECT * FROM planning ;';
    return new Observable((observer: Observer<Planning[]>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const values = this.sqliteService.snakeToCamel(result.values)
          const planning = values as Planning[];
          observer.next(planning);
          observer.complete();
        },
        (error: any) => {
          observer.error(error);
        }
      );
    });
  }
  async getPlanning(jsonPlan: Planning): Promise <Planning> {
    let plan = await this.sqliteService.findOneBy(this.mDb, " planning ", {id: jsonPlan.id});
    if (!plan) {
      if (jsonPlan.planningDate && jsonPlan.prospectId ) {
        let plan = new Planning();
        plan.id = jsonPlan.id;
        plan.planningDate = jsonPlan.planningDate;
        plan.prospectId = jsonPlan.prospectId;
        plan.status = jsonPlan.status;
        plan.synchronized = jsonPlan.synchronized;
        plan=this.sqliteService.camelToSnake(plan)
        await this.sqliteService.save(this.mDb, "planning", plan);
        plan = await this.sqliteService.findOneBy(this.mDb, "planning", { id: jsonPlan.id });
        if (plan) {
          return plan;
        } else {
          return Promise.reject(`failed to getPlanning for id ${jsonPlan.id}`);
        }
      } else {
        let plan = new Planning();
        plan.id = -1;
        return plan;
      }
    } else {
      return plan;
    } 
   }
    // filtre planning  
async getPlanningByDay(currentDate: Date) {
  const selectQuery = `
SELECT * FROM planning WHERE (planning_date = ?) AND (status<>'DELETED')  ORDER BY planning_date `
 const res = await this.mDb.query(selectQuery,[currentDate.setHours(0,0,0,0)]);
 const values = this.sqliteService.snakeToCamel(res.values)
  return values || [];
}
/// prospect

async getProspect(jsonPros: Prospect): Promise <Prospect> {
  let pros = await this.sqliteService.findOneBy(this.mDb, " prospect ", {id: jsonPros.id});
  if (!pros) {
// provisoire la condition 
    if (1) {
      // create a new author
      let pros = new Prospect();
      pros.id = jsonPros.id;
      pros.firstname = jsonPros.firstname;
      pros.lastname = jsonPros.lastname;
      pros.activity = jsonPros.activity;
      pros.potential = jsonPros.potential;
      pros.synchronized = jsonPros.synchronized;
      pros.address = jsonPros.address;
      pros.gsm = jsonPros.gsm;
      pros.phone = jsonPros.phone;
      pros.email = jsonPros.email;
      pros.note = jsonPros.note;
      pros.secretary = jsonPros.secretary;
      pros.grade = jsonPros.grade;
      pros.specialityId = jsonPros.specialityId;
      pros.sectorId = jsonPros.sectorId;
      pros.localityId = jsonPros.localityId;
      pros.lat = jsonPros.lat;
      pros.lng = jsonPros.lng;
      pros.mapAddress = jsonPros.mapAddress;
      pros.status = jsonPros.status;
      pros.validation = jsonPros.validation;
      pros.typeId = jsonPros.typeId;
      pros.establishmentId = jsonPros.establishmentId;
      pros=this.sqliteService.camelToSnake(pros)
      await this.sqliteService.save(this.mDb, "prospect", pros);
      pros = await this.sqliteService.findOneBy(this.mDb, "prospect", { id: jsonPros.id });
      if (pros) {
        return pros;
      } else {
        return Promise.reject(`failed to getProspect for id ${jsonPros.id}`);
      }
    } else {
      // notification not in the database
      let pros = new Prospect();
      pros.id = -1;
      return pros;
    }
  } else {
    return pros;
  } 
 }
 getAllProspects(): Observable<Prospect[]> {
  const selectQuery = 'SELECT * FROM prospect ;';
  return new Observable((observer: Observer<Prospect[]>) => {
    this.mDb.query(selectQuery).then(
      (result: any) => {
        const values = this.sqliteService.snakeToCamel(result.values)
        const prospect = values as Prospect[];
        observer.next(prospect);
        observer.complete();
      },
      (error: any) => {
        observer.error(error);
      }
    );
  });
}
 }
