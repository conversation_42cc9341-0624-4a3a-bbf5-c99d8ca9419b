import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { HomePage } from './home/<USER>';
import { AuthorPage } from './pages/author-posts/author/author.page';
import { AuthorsPage } from './pages/author-posts/authors/authors.page';
import { CategoryPage } from './pages/author-posts/category/category.page';
import { CategoriesPage } from './pages/author-posts/categories/categories.page';
import { PostPage } from './pages/author-posts/post/post.page';
import { PostsPage } from './pages/author-posts/posts/posts.page';
import { EmployeePage } from './pages/employee-dept/employee/employee.page';
import { EmployeesPage } from './pages/employee-dept/employees/employees.page';
import { DepartmentPage } from './pages/employee-dept/department/department.page';
import { DepartmentsPage } from './pages/employee-dept/departments/departments.page';
import { ModalPassphrasePage } from './pages/modal-passphrase/modal-passphrase.page';
import { ModalEncryptionPage } from './pages/modal-encryption/modal-encryption.page';
import { NotificationsComponent } from './components/notifications/notifications.component';
import { BudgetAllocationComponent } from './components/budget-allocation/budget-allocation.component';
import { ExpenseListComponent } from './components/expense-list/expense-list.component';
import { MessageComponent } from './components/message/message.component';
import { PlanningComponent } from './components/planning/planning.component';
import { GoalsComponent } from './components/goals/goals.component';
import { MarketingActionsComponent } from './components/action-marketing/action-marketing.component';
import { LoginComponent } from './components/login/login.component';
import { ActivityCalenderComponent } from './components/activity-calender/activity-calender.component';
import { TroubleShootingComponent } from './components/trouble-shooting/trouble-shooting.component';
import { VisitHistoryComponent } from './components/visit-history/visit-history.component'; 
import { ProspectDetailComponent } from './components/prospect-detail/prospect-detail.component'; 
import { ReportComponent } from './components/report/report.component'; 
import { NextActionRuleComponent } from './components/next-action-rule/next-action-rule.component';

const routes: Routes = [
  {
    path: 'trouble-shooting',
    component: TroubleShootingComponent
  },
  {
    path: 'home',
    component: HomePage
  },
  {
          path: 'calendar',
          component:  ActivityCalenderComponent },
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full'
  },
  {
    path: 'category',
    component: CategoryPage
  },
  {
    path: 'categories',
    component: CategoriesPage
  },
  {
    path: 'author',
    component: AuthorPage
  },
  {
    path: 'authors',
    component: AuthorsPage
  },
  {
    path: 'post',
    component: PostPage
  },
  {
    path: 'posts',
    component: PostsPage
  },
  {
    path: 'employees',
    component: EmployeesPage
  },
  {
    path: 'employee',
    component: EmployeePage
  },
  {
    path: 'department',
    component: DepartmentPage
  },
  {
    path: 'departments',
    component: DepartmentsPage
  },
  {
    path: 'modal-passphrase',
    component: ModalPassphrasePage
  },
  {
    path: 'modal-encryption',
    component: ModalEncryptionPage
  },
  {
    path: 'notifications',
    component:  NotificationsComponent },
    {
      path: 'budget-allocation',
      component:  BudgetAllocationComponent },
      {
        path: 'next-action-rule',
        component:  NextActionRuleComponent },
    {
      path: 'expense-list',
      component:  ExpenseListComponent },

     { path: 'message',
      component:  MessageComponent },

  {
    path: 'planning',
    component:  PlanningComponent },
  {
    path: 'goals',
    component:  GoalsComponent },
    
      {path: 'action-marketing',
      component:  MarketingActionsComponent },
      {
        path: 'login',
        component:  LoginComponent },
        
 
  
  {
    path: 'visit-history',
    component: VisitHistoryComponent
  },
  {
    path: 'report',
    component: ReportComponent
  },
  {
    path: 'prospect-detail',
    component: ProspectDetailComponent
  },
  {
    path: 'prospect-detail/:id',
    component: ProspectDetailComponent
  },
  



];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
