import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private translations: any = {};

  constructor(private http: HttpClient) {}

  loadTranslations(lang: string): Observable<any> {
    return this.http.get(`assets/langues/${lang}`).pipe(
      map((translations: any) => {
        this.translations = translations;
        return translations;
      })
    );
  }

  getTranslation(key: string): string {
    return this.translations[key] || key;
  }

  translate(key: string): string {
    return this.getTranslation(key);
  }
  changeLanguage(lang: string) {
    this.loadTranslations(lang);
  }

  
}
