import { Injectable } from '@angular/core';
import { SQLiteService } from './sqlite.service';
import { Message } from '../models/message';
import { SQLiteDBConnection } from '@capacitor-community/sqlite';
import { DbnameVersionService } from './dbname-version.service';
import { environment } from 'src/environments/environment';
import { TablesUpgrades } from 'src/app/upgrades/tables/tables';

import { Observable, from, Observer } from 'rxjs';
import { MOCK_MESSAGES } from '../mock-data/message';

@Injectable()
export class MessageService {
  private mDb!: SQLiteDBConnection;
  public databaseName: string;
  private versionUpgrades = TablesUpgrades;
  private loadToVersion = TablesUpgrades[TablesUpgrades.length - 1].toVersion;

  constructor(private sqliteService: SQLiteService,
    private dbVerService: DbnameVersionService,
  ) {
    this.databaseName = environment.databaseNames.filter(x => x.name.includes('tables'))[0].name;
  }

  
  async initDatabase(mDb: SQLiteDBConnection){
    this.mDb = mDb;
    await this.createInitialData();
}

  async initializeDatabase() {
    await this.sqliteService.addUpgradeStatement({
      database: this.databaseName,
      upgrade: this.versionUpgrades
    });
    await this.openDatabase();
    this.dbVerService.set(this.databaseName, this.loadToVersion);
    const isData = await this.mDb.query("SELECT * FROM sqlite_sequence");
    if (isData.values!.length === 0) {
      await this.createInitialData();
    }
    if (this.sqliteService.platform === 'web') {
      await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
    }
  }

  async openDatabase() {
    if ((this.sqliteService.native || this.sqliteService.platform === "electron")
      && (await this.sqliteService.isInConfigEncryption()).result
      && (await this.sqliteService.isDatabaseEncrypted(this.databaseName)).result) {
      this.mDb = await this.sqliteService.openDatabase(this.databaseName, true, "secret", this.loadToVersion, false);
    } else {
      this.mDb = await this.sqliteService.openDatabase(this.databaseName, false, "no-encryption", this.loadToVersion, false);
    }
  }
  

  private async createInitialData(): Promise<void> {
    for (const msg of MOCK_MESSAGES) {
      await this.getMessage(msg);
    }
  }

  getAllMessages(): Observable<Message[]> {
    const selectQuery = 'SELECT * FROM message;';
    return new Observable((observer: Observer<Message[]>) => {
      this.mDb.query(selectQuery).then(
        (result: any) => {
          const messages = result.values as Message[];
          observer.next(messages);
          observer.complete();
        },
        (error: any) => {
          observer.error(error);
        }
      );
    });
  }

  async getMessagesByDate(day: number, month: number, year: number): Promise<Message[]> {
    const selectQuery = `
      SELECT * FROM message
      WHERE strftime('%d', date / 1000, 'unixepoch') = ? AND
            strftime('%m', date / 1000, 'unixepoch') = ? AND
            strftime('%Y', date / 1000, 'unixepoch') = ?;
    `;
    const res = await this.mDb.query(selectQuery, [day.toString().padStart(2, '0'), month.toString().padStart(2, '0'), year.toString()]);
    return res.values || [];
  }

  async getMessage(jsonMessage: Message): Promise<Message> {
    let message = await this.sqliteService.findOneBy(this.mDb, "message", { id: jsonMessage.id });
    if (!message) {
      if (jsonMessage.text && jsonMessage.type && jsonMessage.date) {
        let msg = new Message();
        msg.id = jsonMessage.id;
        msg.text = jsonMessage.text;
        msg.type = jsonMessage.type;
        msg.date = jsonMessage.date;
        
       
        if(jsonMessage.user_id){
          msg.user_id = jsonMessage.user_id;
        }
        if(jsonMessage.status){
          msg.status = jsonMessage.status;
        }
        if(jsonMessage.synchronized){
          msg.synchronized = jsonMessage.synchronized;
        }
        await this.sqliteService.save(this.mDb, "message", msg);
        msg = await this.sqliteService.findOneBy(this.mDb, "message", { id: jsonMessage.id });
        if (msg) {
          return msg;
        } else {
          return Promise.reject(`Failed to get message for id ${jsonMessage.id}`);
        }
      } else {
        return Promise.resolve({ id: -1 } as Message);
      }
    } else {
      if(Object.keys(jsonMessage).length > 1) {
        // update and existing author
        const updateMessage = new Message();
        updateMessage.id = jsonMessage.id;
        updateMessage.text = jsonMessage.text;
        updateMessage.type = jsonMessage.type;
        updateMessage.date = jsonMessage.date;
        if(jsonMessage.user_id){
          updateMessage.user_id = jsonMessage.user_id;
        }
        if(jsonMessage.status){
          updateMessage.status = jsonMessage.status;
        }
        if(jsonMessage.synchronized){
          updateMessage.synchronized = jsonMessage.synchronized;
        }
       await this.sqliteService.save(this.mDb, "message", updateMessage, {id: jsonMessage.id});
        message = await this.sqliteService.findOneBy(this.mDb, "message", {id: jsonMessage.id});
        if(message) {
          return message;
        } else {
          return Promise.reject(`failed to getMessage for id ${jsonMessage.id}`);
        }
      } else {
        return message;
      }
    }
  }
  

  saveMessage(message: Message): Observable<void> {
    return from(this.sqliteService.save(this.mDb, "message", message).then(async () => {
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
   
      }
    }));
  }
  
  updateMessage(msgId: number, message: Message): Observable<void> {
    return from(this.sqliteService.save(this.mDb, "message", message, { id: msgId }).then(async () => {
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
       
      }
    }));
  }
  
  deleteMessage(messageId: number): Observable<void> {
    return from(this.sqliteService.remove(this.mDb, "message", { id: messageId }).then(async () => {
      if (this.sqliteService.platform === 'web') {
        await this.sqliteService.sqliteConnection.saveToStore(this.databaseName);
       
      }
    }));
  }
  
}


